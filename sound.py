import os
import pygame

# Initialize pygame mixer
pygame.mixer.init()

# Get the absolute path to the MP3 file
FILENAME = ('Assala - <PERSON><PERSON><PERSON><PERSON> _ Lyrics Video 2023  '
           'أصالة - شبيه روحي(M4A_128K).m4a')
audio_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), FILENAME)

print(f"Attempting to play: {audio_file}")
print(f"File exists: {os.path.exists(audio_file)}")

if os.path.exists(audio_file):
    try:
        # Load and play the audio file
        pygame.mixer.music.load(audio_file)
        pygame.mixer.music.play()

        print("Playing audio... Press Enter to stop.")
        input()  # Wait for user input to stop

        pygame.mixer.music.stop()
        print("Audio stopped.")
    except pygame.error as e:
        print(f"Error playing audio: {e}")
        print("Trying alternative method with playsound...")
        try:
            from playsound import playsound
            playsound(audio_file)
        except ImportError as e2:
            print(f"Playsound also failed: {e2}")
else:
    print("Audio file not found!")

# Clean up
pygame.mixer.quit()