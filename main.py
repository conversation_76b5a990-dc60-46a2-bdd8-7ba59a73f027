"""
Video player application using OpenCV.

This module provides a simple video player that can open and display video files.
The player allows users to view video frames and exit by pressing 'm'.
"""
# mypy: disable-error-code="attr-defined"
import sys
import cv2  # type: ignore

# فتح ملف الفيديو
cap = cv2.VideoCapture('video.mp4')  # type: ignore

# التحقق من أن الفيديو تم فتحه بنجاح
if not cap.isOpened():
    print("خطأ: لم يتم فتح ملف الفيديو.")
    sys.exit(1)

# قراءة الإطارات وعرضها
while True:
    ret, frame = cap.read()

    if not ret:
        print("انتهى الفيديو أو حدث خطأ.")
        break

    cv2.imshow('Video Frame', frame)  # type: ignore

    # اضغط على "m" للخروج
    if cv2.waitKey(25) & 0xFF == ord('m'):  # type: ignore
        print("تم الضغط على m. إنهاء العرض.")
        break

# تنظيف الموارد
cap.release()
cv2.destroyAllWindows()  # type: ignore